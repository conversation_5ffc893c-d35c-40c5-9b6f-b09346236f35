"""
Performance optimization scripts for Windows Performance Optimizer
Specifically designed for PUBG Mobile emulator players
"""
import os
import gc
import psutil
import subprocess
import ctypes
from ctypes import wintypes
import winreg
import time
from typing import Tuple, List, Dict
from utils import run_command, log_action, get_emulator_processes, set_registry_value

# Constants for advanced optimization
UNNECESSARY_SERVICES = [
    "DiagTrack",  # Diagnostics Tracking Service
    "dmwappushservice",  # Diagnostic Management Service
    "SysMain",  # Superfetch (can be helpful for HDDs but not SSDs)
    "TrkWks",  # Distributed Link Tracking Client
    "WSearch",  # Windows Search
    "Fax",  # Fax Service
    "XblAuthManager",  # Xbox Live Auth Manager
    "XblGameSave",  # Xbox Live Game Save
    "XboxNetApiSvc",  # Xbox Live Networking Service
    "lfsvc",  # Geolocation Service
    "MapsBroker",  # Downloaded Maps Manager
    "PhoneSvc",  # Phone Service
    "WpcMonSvc",  # Parental Controls
    "RetailDemo",  # Retail Demo Service
    "RemoteRegistry",  # Remote Registry
    "SCardSvr",  # Smart Card
    "SSDPSRV",  # SSDP Discovery
    "upnphost",  # UPnP Device Host
    "wercplsupport",  # Problem Reports Control Panel Support
    "WerSvc",  # Windows Error Reporting Service
]

ENHANCED_UNNECESSARY_PROCESSES = [
    "OneDrive.exe",
    "Spotify.exe",
    "Discord.exe",
    "Skype.exe",
    "Teams.exe",
    "Dropbox.exe",
    "AdobeARM.exe",
    "CCXProcess.exe",
    "CCLibrary.exe",
    "Creative Cloud.exe",
    "EADMProxy.exe",
    "Origin.exe",
    "EpicGamesLauncher.exe",
    "Battle.net.exe",
    "Steam.exe",
    "chrome.exe",
    "firefox.exe",
    "edge.exe",
    "opera.exe",
    "obs64.exe",
    "obs32.exe",
    "photoshop.exe",
    "illustrator.exe",
    "code.exe",
    "devenv.exe",
    "torrent.exe",
    "utorrent.exe",
    "bittorrent.exe"
]

POWER_CFG_HIGH_PERF = "8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c"


class PerformanceOptimizer:
    """Main class for performance optimization operations"""
    
    def __init__(self):
        self.kernel32 = ctypes.windll.kernel32
        self.ntdll = ctypes.windll.ntdll
    
    def clear_memory_cache(self) -> Tuple[bool, str]:
        """Clear system memory cache and reduce RAM usage"""
        try:
            # Force garbage collection
            gc.collect()
            
            # Clear standby memory using Windows API
            success = self._clear_standby_memory()
            
            # Clear working set for current process
            try:
                handle = self.kernel32.GetCurrentProcess()
                self.kernel32.SetProcessWorkingSetSize(handle, -1, -1)
            except:
                pass
            
            log_action("Clear Memory Cache", success, "Cleared system memory cache")
            return success, "Memory cache cleared successfully" if success else "Failed to clear memory cache"
            
        except Exception as e:
            log_action("Clear Memory Cache", False, str(e))
            return False, f"Error clearing memory cache: {str(e)}"
    
    def _clear_standby_memory(self) -> bool:
        """Clear standby memory using Windows API"""
        try:
            # Define constants
            SYSTEM_MEMORY_LIST_INFORMATION = 80
            MemoryPurgeStandbyList = 4
            
            # Try to clear standby memory
            status = self.ntdll.NtSetSystemInformation(
                SYSTEM_MEMORY_LIST_INFORMATION,
                ctypes.byref(ctypes.c_int(MemoryPurgeStandbyList)),
                ctypes.sizeof(ctypes.c_int)
            )
            return status == 0
        except:
            return False
    
    def clear_dns_cache(self) -> Tuple[bool, str]:
        """Clear DNS cache to improve network performance"""
        try:
            success, output, error = run_command("ipconfig /flushdns")
            
            if success and "successfully flushed" in output.lower():
                log_action("Clear DNS Cache", True, "DNS cache flushed")
                return True, "DNS cache cleared successfully"
            else:
                log_action("Clear DNS Cache", False, error)
                return False, f"Failed to clear DNS cache: {error}"
                
        except Exception as e:
            log_action("Clear DNS Cache", False, str(e))
            return False, f"Error clearing DNS cache: {str(e)}"
    
    def clear_temp_files(self) -> Tuple[bool, str]:
        """Clear temporary files to free up disk space"""
        try:
            temp_dirs = [
                os.environ.get('TEMP', ''),
                os.environ.get('TMP', ''),
                'C:\\Windows\\Temp',
                'C:\\Windows\\Prefetch'
            ]
            
            files_deleted = 0
            total_size = 0
            
            for temp_dir in temp_dirs:
                if os.path.exists(temp_dir):
                    deleted, size = self._clean_directory(temp_dir)
                    files_deleted += deleted
                    total_size += size
            
            # Clear browser caches
            browser_deleted, browser_size = self._clear_browser_caches()
            files_deleted += browser_deleted
            total_size += browser_size
            
            message = f"Deleted {files_deleted} files, freed {total_size / (1024*1024):.1f} MB"
            log_action("Clear Temp Files", True, message)
            return True, message
            
        except Exception as e:
            log_action("Clear Temp Files", False, str(e))
            return False, f"Error clearing temp files: {str(e)}"
    
    def _clean_directory(self, directory: str) -> Tuple[int, int]:
        """Clean files in a directory"""
        files_deleted = 0
        total_size = 0
        
        try:
            for root, dirs, files in os.walk(directory):
                for file in files:
                    try:
                        file_path = os.path.join(root, file)
                        if os.path.exists(file_path):
                            size = os.path.getsize(file_path)
                            os.remove(file_path)
                            files_deleted += 1
                            total_size += size
                    except:
                        continue
        except:
            pass
        
        return files_deleted, total_size
    
    def _clear_browser_caches(self) -> Tuple[int, int]:
        """Clear browser caches"""
        files_deleted = 0
        total_size = 0
        
        # Chrome cache
        chrome_cache = os.path.expanduser("~\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Cache")
        if os.path.exists(chrome_cache):
            deleted, size = self._clean_directory(chrome_cache)
            files_deleted += deleted
            total_size += size
        
        # Firefox cache
        firefox_cache = os.path.expanduser("~\\AppData\\Local\\Mozilla\\Firefox\\Profiles")
        if os.path.exists(firefox_cache):
            for profile in os.listdir(firefox_cache):
                cache_dir = os.path.join(firefox_cache, profile, "cache2")
                if os.path.exists(cache_dir):
                    deleted, size = self._clean_directory(cache_dir)
                    files_deleted += deleted
                    total_size += size
        
        return files_deleted, total_size
    
    def set_high_performance_mode(self) -> Tuple[bool, str]:
        """Set Windows to high performance power plan"""
        try:
            # Get high performance GUID
            success, output, error = run_command("powercfg /list")
            
            if not success:
                return False, f"Failed to get power plans: {error}"
            
            # Look for high performance plan
            high_perf_guid = None
            for line in output.split('\n'):
                if 'high performance' in line.lower():
                    # Extract GUID
                    parts = line.split()
                    for part in parts:
                        if len(part) == 36 and part.count('-') == 4:
                            high_perf_guid = part
                            break
                    break
            
            if not high_perf_guid:
                # Create high performance plan if not found
                success, output, error = run_command("powercfg /duplicatescheme 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c")
                if success:
                    high_perf_guid = "8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c"
            
            if high_perf_guid:
                success, output, error = run_command(f"powercfg /setactive {high_perf_guid}")
                if success:
                    log_action("Set High Performance", True, "High performance mode activated")
                    return True, "High performance mode activated"
            
            log_action("Set High Performance", False, "Could not activate high performance mode")
            return False, "Could not activate high performance mode"
            
        except Exception as e:
            log_action("Set High Performance", False, str(e))
            return False, f"Error setting high performance mode: {str(e)}"
    
    def optimize_network_settings(self) -> Tuple[bool, str]:
        """Optimize network settings for gaming"""
        try:
            optimizations = []
            
            # Reset network adapters
            success, _, _ = run_command("netsh winsock reset")
            if success:
                optimizations.append("Winsock reset")
            
            # Reset TCP/IP stack
            success, _, _ = run_command("netsh int ip reset")
            if success:
                optimizations.append("TCP/IP reset")
            
            # Flush ARP cache
            success, _, _ = run_command("arp -d *")
            if success:
                optimizations.append("ARP cache cleared")
            
            # Optimize TCP settings via registry
            tcp_optimizations = self._optimize_tcp_registry()
            optimizations.extend(tcp_optimizations)
            
            message = f"Network optimizations applied: {', '.join(optimizations)}"
            log_action("Optimize Network", True, message)
            return True, message
            
        except Exception as e:
            log_action("Optimize Network", False, str(e))
            return False, f"Error optimizing network: {str(e)}"
    
    def _optimize_tcp_registry(self) -> List[str]:
        """Optimize TCP settings in registry"""
        optimizations = []
        tcp_key = r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters"
        
        # TCP optimizations for gaming
        settings = {
            "TcpAckFrequency": 1,
            "TCPNoDelay": 1,
            "TcpDelAckTicks": 0,
            "DefaultTTL": 64
        }
        
        for setting, value in settings.items():
            if set_registry_value(tcp_key, setting, value):
                optimizations.append(f"TCP {setting}")
        
        return optimizations

    def kill_unnecessary_processes(self) -> Tuple[bool, str]:
        """Kill unnecessary processes to free up resources"""
        try:
            # Processes that are safe to kill and consume resources
            unnecessary_processes = [
                'chrome.exe', 'firefox.exe', 'edge.exe', 'opera.exe',  # Browsers
                'spotify.exe', 'discord.exe', 'skype.exe', 'teams.exe',  # Communication
                'steam.exe', 'epicgameslauncher.exe', 'origin.exe',  # Game launchers
                'obs64.exe', 'obs32.exe',  # Recording software
                'photoshop.exe', 'illustrator.exe',  # Adobe
                'code.exe', 'devenv.exe',  # IDEs
                'torrent.exe', 'utorrent.exe', 'bittorrent.exe'  # Torrents
            ]

            killed_processes = []
            emulator_processes = get_emulator_processes()

            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    proc_name = proc.info['name'].lower()

                    # Don't kill emulator processes
                    if proc.info['name'] in emulator_processes:
                        continue

                    # Kill unnecessary processes
                    if any(unnecessary in proc_name for unnecessary in unnecessary_processes):
                        proc.terminate()
                        killed_processes.append(proc.info['name'])

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            message = f"Killed {len(killed_processes)} unnecessary processes"
            if killed_processes:
                message += f": {', '.join(killed_processes[:5])}"
                if len(killed_processes) > 5:
                    message += f" and {len(killed_processes) - 5} more"

            log_action("Kill Unnecessary Processes", True, message)
            return True, message

        except Exception as e:
            log_action("Kill Unnecessary Processes", False, str(e))
            return False, f"Error killing processes: {str(e)}"

    def optimize_emulator_priority(self) -> Tuple[bool, str]:
        """Set high priority for emulator processes"""
        try:
            emulator_names = get_emulator_processes()
            optimized_emulators = []

            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if proc.info['name'] in emulator_names:
                        # Set high priority
                        proc.nice(psutil.HIGH_PRIORITY_CLASS)
                        optimized_emulators.append(proc.info['name'])

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if optimized_emulators:
                message = f"Set high priority for: {', '.join(optimized_emulators)}"
                log_action("Optimize Emulator Priority", True, message)
                return True, message
            else:
                message = "No emulator processes found to optimize"
                log_action("Optimize Emulator Priority", False, message)
                return False, message

        except Exception as e:
            log_action("Optimize Emulator Priority", False, str(e))
            return False, f"Error optimizing emulator priority: {str(e)}"

    def disable_windows_game_mode(self) -> Tuple[bool, str]:
        """Disable Windows Game Mode which can interfere with emulators"""
        try:
            game_mode_key = r"SOFTWARE\Microsoft\GameBar"

            # Disable Game Mode
            success1 = set_registry_value(game_mode_key, "AllowAutoGameMode", 0, winreg.REG_DWORD, winreg.HKEY_CURRENT_USER)
            success2 = set_registry_value(game_mode_key, "AutoGameModeEnabled", 0, winreg.REG_DWORD, winreg.HKEY_CURRENT_USER)

            # Disable Game Bar
            success3 = set_registry_value(game_mode_key, "UseNexusForGameBarEnabled", 0, winreg.REG_DWORD, winreg.HKEY_CURRENT_USER)

            if success1 or success2 or success3:
                message = "Windows Game Mode disabled"
                log_action("Disable Game Mode", True, message)
                return True, message
            else:
                message = "Failed to disable Game Mode"
                log_action("Disable Game Mode", False, message)
                return False, message

        except Exception as e:
            log_action("Disable Game Mode", False, str(e))
            return False, f"Error disabling Game Mode: {str(e)}"

    def empty_recycle_bin(self) -> Tuple[bool, str]:
        """Empty the recycle bin"""
        try:
            success, output, error = run_command("rd /s /q C:\\$Recycle.Bin")

            # Alternative method using PowerShell
            if not success:
                success, output, error = run_command(
                    "powershell.exe -Command \"Clear-RecycleBin -Force\""
                )

            if success:
                message = "Recycle bin emptied"
                log_action("Empty Recycle Bin", True, message)
                return True, message
            else:
                message = f"Failed to empty recycle bin: {error}"
                log_action("Empty Recycle Bin", False, message)
                return False, message

        except Exception as e:
            log_action("Empty Recycle Bin", False, str(e))
            return False, f"Error emptying recycle bin: {str(e)}"

    def optimize_virtual_memory(self) -> Tuple[bool, str]:
        """Optimize virtual memory settings"""
        try:
            # Get total RAM
            total_ram_gb = psutil.virtual_memory().total / (1024**3)

            # Calculate optimal page file size (1.5x RAM)
            optimal_size_mb = int(total_ram_gb * 1.5 * 1024)

            # Set virtual memory via registry
            vm_key = r"SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management"

            # Disable automatic page file management
            success1 = set_registry_value(vm_key, "DisablePagingExecutive", 1)

            # Set page file size
            success2 = set_registry_value(vm_key, "PagingFiles", f"C:\\pagefile.sys {optimal_size_mb} {optimal_size_mb}", winreg.REG_MULTI_SZ)

            if success1 or success2:
                message = f"Virtual memory optimized (Page file: {optimal_size_mb} MB)"
                log_action("Optimize Virtual Memory", True, message)
                return True, message
            else:
                message = "Failed to optimize virtual memory"
                log_action("Optimize Virtual Memory", False, message)
                return False, message

        except Exception as e:
            log_action("Optimize Virtual Memory", False, str(e))
            return False, f"Error optimizing virtual memory: {str(e)}"

    def run_full_optimization(self, profile: str = "pubg_mobile") -> Tuple[bool, List[str]]:
        """Run full optimization based on profile"""
        results = []

        optimization_functions = {
            "clear_memory": self.clear_memory_cache,
            "clear_dns": self.clear_dns_cache,
            "clear_temp_files": self.clear_temp_files,
            "set_high_performance": self.set_high_performance_mode,
            "optimize_network": self.optimize_network_settings,
            "kill_unnecessary_processes": self.kill_unnecessary_processes,
            "optimize_emulator_priority": self.optimize_emulator_priority,
            "disable_game_mode": self.disable_windows_game_mode,
            "empty_recycle_bin": self.empty_recycle_bin,
            "clear_standby_memory": self.clear_memory_cache,
            "optimize_virtual_memory": self.optimize_virtual_memory,
            "disable_services": self.disable_unnecessary_services,
            "optimize_visual_effects": self.optimize_visual_effects,
            "disable_xbox_features": self.disable_xbox_features,
            "disable_tips_and_ads": self.disable_windows_tips_and_ads,
            "enhanced_process_cleanup": self.enhanced_process_cleanup,
            "advanced_disk_cleanup": self.advanced_disk_cleanup,
            "optimize_startup": self.optimize_startup_programs,
            "optimize_registry": self.optimize_registry_for_gaming,
            "optimize_gpu": self.optimize_gpu_settings,
            "optimize_cpu": self.optimize_cpu_priority,
            "advanced_memory": self.advanced_memory_optimization
        }

        # Default optimizations for PUBG Mobile
        if profile == "pubg_mobile":
            optimizations_to_run = [
                "clear_memory", "clear_dns", "clear_temp_files",
                "set_high_performance", "optimize_network",
                "enhanced_process_cleanup", "optimize_emulator_priority",
                "disable_game_mode", "disable_services", "optimize_visual_effects",
                "disable_xbox_features", "disable_tips_and_ads"
            ]
        elif profile == "basic":
            optimizations_to_run = [
                "clear_temp_files", "clear_dns", "empty_recycle_bin",
                "optimize_visual_effects"
            ]
        elif profile == "advanced":
            optimizations_to_run = [
                "clear_memory", "clear_dns", "advanced_disk_cleanup",
                "set_high_performance", "optimize_network",
                "enhanced_process_cleanup", "optimize_emulator_priority",
                "disable_game_mode", "disable_services", "optimize_visual_effects",
                "disable_xbox_features", "disable_tips_and_ads", "optimize_startup",
                "optimize_registry", "optimize_gpu", "optimize_cpu", "advanced_memory"
            ]
        else:  # gaming profile
            optimizations_to_run = [
                "clear_memory", "clear_dns", "clear_temp_files",
                "set_high_performance", "optimize_network",
                "enhanced_process_cleanup", "disable_xbox_features"
            ]

        success_count = 0
        for optimization in optimizations_to_run:
            if optimization in optimization_functions:
                try:
                    success, message = optimization_functions[optimization]()
                    results.append(f"{'✓' if success else '✗'} {message}")
                    if success:
                        success_count += 1
                except Exception as e:
                    results.append(f"✗ Error in {optimization}: {str(e)}")

        overall_success = success_count > len(optimizations_to_run) // 2
        log_action("Full Optimization", overall_success, f"Completed {success_count}/{len(optimizations_to_run)} optimizations")

        return overall_success, results

    def run_comprehensive_optimization(self) -> Tuple[bool, List[str]]:
        """Run the most comprehensive optimization available - equivalent to the original script"""
        results = []

        print("=== AbuSaker Tools - Comprehensive Windows Optimization ===")
        print("Running all available optimizations for maximum performance...")

        # Run all optimization functions in optimal order
        optimization_sequence = [
            ("Setting High Performance Mode", self.set_high_performance_mode),
            ("Disabling Unnecessary Services", self.disable_unnecessary_services),
            ("Enhanced Process Cleanup", self.enhanced_process_cleanup),
            ("Advanced Disk Cleanup", self.advanced_disk_cleanup),
            ("Optimizing Visual Effects", self.optimize_visual_effects),
            ("Disabling Xbox Features", self.disable_xbox_features),
            ("Disabling Windows Tips & Ads", self.disable_windows_tips_and_ads),
            ("Optimizing Network Settings", self.optimize_network_settings),
            ("Optimizing Registry", self.optimize_registry_for_gaming),
            ("Optimizing GPU Settings", self.optimize_gpu_settings),
            ("Optimizing CPU Priority", self.optimize_cpu_priority),
            ("Advanced Memory Optimization", self.advanced_memory_optimization),
            ("Optimizing Startup Programs", self.optimize_startup_programs),
            ("Clearing Memory Cache", self.clear_memory_cache),
            ("Clearing DNS Cache", self.clear_dns_cache)
        ]

        success_count = 0
        total_operations = len(optimization_sequence)

        for description, optimization_func in optimization_sequence:
            try:
                print(f"\n{description}...")
                success, message = optimization_func()
                status = "✓" if success else "✗"
                results.append(f"{status} {description}: {message}")
                if success:
                    success_count += 1
                print(f"{status} {message}")
            except Exception as e:
                error_msg = f"Error in {description}: {str(e)}"
                results.append(f"✗ {error_msg}")
                print(f"✗ {error_msg}")

        overall_success = success_count > total_operations // 2
        completion_rate = (success_count / total_operations) * 100

        summary = f"Comprehensive optimization completed: {success_count}/{total_operations} operations successful ({completion_rate:.1f}%)"
        log_action("Comprehensive Optimization", overall_success, summary)

        print(f"\n=== Optimization Complete ===")
        print(summary)
        print("Some changes may require a restart to take full effect.")

        return overall_success, results

    def optimize_registry_for_gaming(self) -> Tuple[bool, str]:
        """Optimize Windows registry settings for gaming performance"""
        try:
            optimizations = []

            # Disable Windows Update automatic restart
            success1 = set_registry_value(
                r"SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU",
                "NoAutoRebootWithLoggedOnUsers", 1, winreg.REG_DWORD, winreg.HKEY_LOCAL_MACHINE
            )
            if success1:
                optimizations.append("Disabled automatic Windows Update restart")

            # Optimize visual effects for performance
            success2 = set_registry_value(
                r"Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects",
                "VisualFXSetting", 2, winreg.REG_DWORD, winreg.HKEY_CURRENT_USER
            )
            if success2:
                optimizations.append("Set visual effects for best performance")

            # Optimize memory management
            success3 = set_registry_value(
                r"SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management",
                "LargeSystemCache", 0, winreg.REG_DWORD, winreg.HKEY_LOCAL_MACHINE
            )
            if success3:
                optimizations.append("Optimized memory management for applications")

            if optimizations:
                message = f"Registry optimized: {', '.join(optimizations)}"
                log_action("Registry Optimization", True, message)
                return True, message
            else:
                message = "No registry optimizations could be applied"
                log_action("Registry Optimization", False, message)
                return False, message

        except Exception as e:
            log_action("Registry Optimization", False, str(e))
            return False, f"Error optimizing registry: {str(e)}"

    def optimize_gpu_settings(self) -> Tuple[bool, str]:
        """Optimize GPU settings for gaming performance"""
        try:
            optimizations = []

            # Set GPU scheduling to hardware accelerated
            success1 = set_registry_value(
                r"SYSTEM\CurrentControlSet\Control\GraphicsDrivers",
                "HwSchMode", 2, winreg.REG_DWORD, winreg.HKEY_LOCAL_MACHINE
            )
            if success1:
                optimizations.append("Enabled hardware-accelerated GPU scheduling")

            if optimizations:
                message = f"GPU optimized: {', '.join(optimizations)}"
                log_action("GPU Optimization", True, message)
                return True, message
            else:
                message = "GPU settings checked (no changes needed)"
                log_action("GPU Optimization", True, message)
                return True, message

        except Exception as e:
            log_action("GPU Optimization", False, str(e))
            return False, f"Error optimizing GPU: {str(e)}"

    def optimize_cpu_priority(self) -> Tuple[bool, str]:
        """Optimize CPU priority settings for gaming"""
        try:
            optimizations = []

            # Set processor scheduling for programs (not background services)
            success1 = set_registry_value(
                r"SYSTEM\CurrentControlSet\Control\PriorityControl",
                "Win32PrioritySeparation", 38, winreg.REG_DWORD, winreg.HKEY_LOCAL_MACHINE
            )
            if success1:
                optimizations.append("Set CPU priority for foreground applications")

            if optimizations:
                message = f"CPU optimized: {', '.join(optimizations)}"
                log_action("CPU Optimization", True, message)
                return True, message
            else:
                message = "CPU settings checked (no changes needed)"
                log_action("CPU Optimization", True, message)
                return True, message

        except Exception as e:
            log_action("CPU Optimization", False, str(e))
            return False, f"Error optimizing CPU: {str(e)}"

    def advanced_memory_optimization(self) -> Tuple[bool, str]:
        """Advanced memory optimization for gaming"""
        try:
            optimizations = []

            # Clear recycle bin first
            success1, _ = self.empty_recycle_bin()
            if success1:
                optimizations.append("Cleared recycle bin")

            # Optimize paging file
            success2 = set_registry_value(
                r"SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management",
                "ClearPageFileAtShutdown", 1, winreg.REG_DWORD, winreg.HKEY_LOCAL_MACHINE
            )
            if success2:
                optimizations.append("Enabled page file clearing at shutdown")

            # Disable superfetch for SSDs
            success3 = set_registry_value(
                r"SYSTEM\CurrentControlSet\Services\SysMain",
                "Start", 4, winreg.REG_DWORD, winreg.HKEY_LOCAL_MACHINE
            )
            if success3:
                optimizations.append("Disabled Superfetch service")

            # Force memory cleanup
            success4, _ = self.clear_memory_cache()
            if success4:
                optimizations.append("Forced memory cleanup")

            if optimizations:
                message = f"Memory optimized: {', '.join(optimizations)}"
                log_action("Advanced Memory Optimization", True, message)
                return True, message
            else:
                message = "Memory optimization completed"
                log_action("Advanced Memory Optimization", True, message)
                return True, message

        except Exception as e:
            log_action("Advanced Memory Optimization", False, str(e))
            return False, f"Error in memory optimization: {str(e)}"

    def disable_unnecessary_services(self) -> Tuple[bool, str]:
        """Disable unnecessary Windows services for better performance"""
        try:
            disabled_services = []
            failed_services = []

            for service in UNNECESSARY_SERVICES:
                try:
                    # Check if service exists first
                    success, output, error = run_command(f"sc query {service}", ignore_errors=True)
                    if not success:
                        continue  # Service doesn't exist

                    # Stop the service (ignore errors as service might already be stopped)
                    run_command(f"sc stop {service}", ignore_errors=True)
                    time.sleep(0.5)  # Brief pause between operations

                    # Disable the service
                    success, _, error = run_command(f"sc config {service} start= disabled", ignore_errors=True)
                    if success:
                        disabled_services.append(service)
                    else:
                        failed_services.append(service)

                except Exception:
                    failed_services.append(service)
                    continue

            if disabled_services:
                message = f"Disabled {len(disabled_services)} services: {', '.join(disabled_services[:3])}"
                if len(disabled_services) > 3:
                    message += f" and {len(disabled_services) - 3} more"
                log_action("Disable Services", True, message)
                return True, message
            else:
                message = "No services could be disabled (may already be optimized)"
                log_action("Disable Services", False, message)
                return False, message

        except Exception as e:
            log_action("Disable Services", False, str(e))
            return False, f"Error disabling services: {str(e)}"

    def optimize_visual_effects(self) -> Tuple[bool, str]:
        """Disable visual effects for better performance"""
        try:
            optimizations = []

            # Set visual effects for best performance
            success1 = set_registry_value(
                r"Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects",
                "VisualFXSetting", 2, winreg.REG_DWORD, winreg.HKEY_CURRENT_USER
            )
            if success1:
                optimizations.append("Visual effects set for best performance")

            # Disable animations
            success2 = set_registry_value(
                r"Control Panel\Desktop",
                "UserPreferencesMask", b'\x90\x12\x03\x80\x10\x00\x00\x00', winreg.REG_BINARY, winreg.HKEY_CURRENT_USER
            )
            if success2:
                optimizations.append("Disabled animations")

            # Disable menu show delay
            success3 = set_registry_value(
                r"Control Panel\Desktop",
                "MenuShowDelay", "0", winreg.REG_SZ, winreg.HKEY_CURRENT_USER
            )
            if success3:
                optimizations.append("Removed menu delays")

            if optimizations:
                message = f"Visual effects optimized: {', '.join(optimizations)}"
                log_action("Optimize Visual Effects", True, message)
                return True, message
            else:
                message = "Visual effects already optimized"
                log_action("Optimize Visual Effects", True, message)
                return True, message

        except Exception as e:
            log_action("Optimize Visual Effects", False, str(e))
            return False, f"Error optimizing visual effects: {str(e)}"

    def disable_xbox_features(self) -> Tuple[bool, str]:
        """Disable Xbox Game Bar and related features"""
        try:
            optimizations = []

            # Disable Xbox Game Bar
            success1 = set_registry_value(
                r"Software\Microsoft\Windows\CurrentVersion\GameDVR",
                "AppCaptureEnabled", 0, winreg.REG_DWORD, winreg.HKEY_CURRENT_USER
            )
            if success1:
                optimizations.append("Disabled Xbox Game Bar")

            # Disable Game DVR
            success2 = set_registry_value(
                r"Software\Microsoft\Windows\CurrentVersion\GameDVR",
                "GameDVR_Enabled", 0, winreg.REG_DWORD, winreg.HKEY_CURRENT_USER
            )
            if success2:
                optimizations.append("Disabled Game DVR")

            # Disable Game Mode
            success3 = set_registry_value(
                r"Software\Microsoft\GameBar",
                "AllowAutoGameMode", 0, winreg.REG_DWORD, winreg.HKEY_CURRENT_USER
            )
            if success3:
                optimizations.append("Disabled Auto Game Mode")

            if optimizations:
                message = f"Xbox features disabled: {', '.join(optimizations)}"
                log_action("Disable Xbox Features", True, message)
                return True, message
            else:
                message = "Xbox features already disabled"
                log_action("Disable Xbox Features", True, message)
                return True, message

        except Exception as e:
            log_action("Disable Xbox Features", False, str(e))
            return False, f"Error disabling Xbox features: {str(e)}"

    def disable_windows_tips_and_ads(self) -> Tuple[bool, str]:
        """Disable Windows tips, tricks, and ads"""
        try:
            optimizations = []

            # Disable tips and suggestions
            success1 = set_registry_value(
                r"Software\Microsoft\Windows\CurrentVersion\ContentDeliveryManager",
                "SubscribedContent-338388Enabled", 0, winreg.REG_DWORD, winreg.HKEY_CURRENT_USER
            )
            if success1:
                optimizations.append("Disabled tips and suggestions")

            # Disable Start Menu suggestions
            success2 = set_registry_value(
                r"Software\Microsoft\Windows\CurrentVersion\ContentDeliveryManager",
                "SystemPaneSuggestionsEnabled", 0, winreg.REG_DWORD, winreg.HKEY_CURRENT_USER
            )
            if success2:
                optimizations.append("Disabled Start Menu suggestions")

            # Disable lock screen tips
            success3 = set_registry_value(
                r"Software\Microsoft\Windows\CurrentVersion\ContentDeliveryManager",
                "RotatingLockScreenEnabled", 0, winreg.REG_DWORD, winreg.HKEY_CURRENT_USER
            )
            if success3:
                optimizations.append("Disabled lock screen tips")

            if optimizations:
                message = f"Windows tips disabled: {', '.join(optimizations)}"
                log_action("Disable Tips and Ads", True, message)
                return True, message
            else:
                message = "Windows tips already disabled"
                log_action("Disable Tips and Ads", True, message)
                return True, message

        except Exception as e:
            log_action("Disable Tips and Ads", False, str(e))
            return False, f"Error disabling tips and ads: {str(e)}"

    def enhanced_process_cleanup(self) -> Tuple[bool, str]:
        """Enhanced process cleanup with comprehensive process list"""
        try:
            killed_processes = []
            emulator_processes = get_emulator_processes()

            for proc in psutil.process_iter(['pid', 'name', 'memory_info']):
                try:
                    proc_name = proc.info['name']

                    # Don't kill emulator processes
                    if proc_name in emulator_processes:
                        continue

                    # Don't kill critical system processes
                    if proc_name.lower() in ['explorer.exe', 'winlogon.exe', 'csrss.exe', 'smss.exe', 'wininit.exe']:
                        continue

                    # Kill processes from enhanced list
                    if proc_name in ENHANCED_UNNECESSARY_PROCESSES:
                        proc.terminate()
                        killed_processes.append(proc_name)

                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue

            message = f"Terminated {len(killed_processes)} unnecessary processes"
            if killed_processes:
                message += f": {', '.join(set(killed_processes[:5]))}"  # Remove duplicates and limit display
                if len(set(killed_processes)) > 5:
                    message += f" and {len(set(killed_processes)) - 5} more"

            log_action("Enhanced Process Cleanup", True, message)
            return True, message

        except Exception as e:
            log_action("Enhanced Process Cleanup", False, str(e))
            return False, f"Error in process cleanup: {str(e)}"

    def advanced_disk_cleanup(self) -> Tuple[bool, str]:
        """Advanced disk cleanup including system files"""
        try:
            optimizations = []
            total_freed = 0

            # Run Windows Disk Cleanup
            success, _, _ = run_command("cleanmgr /sagerun:1", ignore_errors=True)
            if success:
                optimizations.append("Windows Disk Cleanup")

            # Clear Windows Update cache
            try:
                # Stop Windows Update service
                run_command("net stop wuauserv", ignore_errors=True)

                # Clear SoftwareDistribution folder
                update_cache = "C:\\Windows\\SoftwareDistribution\\Download"
                if os.path.exists(update_cache):
                    deleted, size = self._clean_directory(update_cache)
                    if deleted > 0:
                        optimizations.append(f"Windows Update cache ({size / (1024*1024):.1f} MB)")
                        total_freed += size

                # Restart Windows Update service
                run_command("net start wuauserv", ignore_errors=True)

            except Exception:
                pass

            # Clear Windows Logs
            try:
                logs_dir = "C:\\Windows\\Logs"
                if os.path.exists(logs_dir):
                    deleted, size = self._clean_directory(logs_dir)
                    if deleted > 0:
                        optimizations.append(f"Windows logs ({size / (1024*1024):.1f} MB)")
                        total_freed += size
            except Exception:
                pass

            # Clear thumbnail cache
            try:
                thumb_cache = os.path.expanduser("~\\AppData\\Local\\Microsoft\\Windows\\Explorer")
                if os.path.exists(thumb_cache):
                    deleted, size = self._clean_directory(thumb_cache)
                    if deleted > 0:
                        optimizations.append(f"Thumbnail cache ({size / (1024*1024):.1f} MB)")
                        total_freed += size
            except Exception:
                pass

            if optimizations:
                message = f"Advanced cleanup completed: {', '.join(optimizations[:3])}"
                if len(optimizations) > 3:
                    message += f" and {len(optimizations) - 3} more"
                message += f" (Total: {total_freed / (1024*1024):.1f} MB freed)"
                log_action("Advanced Disk Cleanup", True, message)
                return True, message
            else:
                message = "Advanced disk cleanup completed (no significant files to clean)"
                log_action("Advanced Disk Cleanup", True, message)
                return True, message

        except Exception as e:
            log_action("Advanced Disk Cleanup", False, str(e))
            return False, f"Error in advanced disk cleanup: {str(e)}"

    def optimize_startup_programs(self) -> Tuple[bool, str]:
        """Optimize startup programs for faster boot"""
        try:
            optimizations = []

            # Disable unnecessary startup programs via registry
            startup_locations = [
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run",
                r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Run"
            ]

            unnecessary_startups = [
                "Spotify", "Discord", "Skype", "Teams", "Steam", "Origin",
                "EpicGamesLauncher", "Adobe", "CCXProcess", "OneDrive"
            ]

            for location in startup_locations:
                try:
                    with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, location, 0, winreg.KEY_READ | winreg.KEY_WRITE) as key:
                        i = 0
                        while True:
                            try:
                                name, value, _ = winreg.EnumValue(key, i)
                                if any(startup in name for startup in unnecessary_startups):
                                    winreg.DeleteValue(key, name)
                                    optimizations.append(f"Disabled {name}")
                                i += 1
                            except WindowsError:
                                break
                except Exception:
                    continue

            if optimizations:
                message = f"Startup optimization: {', '.join(optimizations[:3])}"
                if len(optimizations) > 3:
                    message += f" and {len(optimizations) - 3} more disabled"
                log_action("Optimize Startup", True, message)
                return True, message
            else:
                message = "Startup programs already optimized"
                log_action("Optimize Startup", True, message)
                return True, message

        except Exception as e:
            log_action("Optimize Startup", False, str(e))
            return False, f"Error optimizing startup: {str(e)}"
