"""
Test script for the enhanced logging system
This script tests the new logging features without running the full GUI
"""

import sys
import os
import time
from datetime import datetime

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_logging():
    """Test the enhanced logging system components"""
    print("🧪 Testing Enhanced Logging System")
    print("=" * 50)
    
    # Test 1: Import the new classes
    try:
        from main import LogLevel, LogMessage, EnhancedLogger
        print("✅ Successfully imported enhanced logging classes")
    except ImportError as e:
        print(f"❌ Failed to import enhanced logging classes: {e}")
        return False
    
    # Test 2: Create log messages
    try:
        # Test different log levels
        test_messages = [
            LogMessage(LogLevel.INFO, "System initialization started"),
            LogMessage(LogLevel.SUCCESS, "Memory cache cleared successfully", "Memory Cleanup"),
            LogMessage(LogLevel.WARNING, "High CPU usage detected: 85%", "System Monitor"),
            LogMessage(LogLevel.ERROR, "Failed to disable service: Access denied", "Service Management"),
            LogMessage(LogLevel.PROGRESS, "Optimization in progress", "Comprehensive Optimization", 45),
        ]
        
        print("\n📝 Testing log message formatting:")
        for msg in test_messages:
            formatted = msg.format_message()
            print(f"  {formatted}")
        
        print("✅ Log message formatting works correctly")
    except Exception as e:
        print(f"❌ Error testing log messages: {e}")
        return False
    
    # Test 3: Test log level icons
    try:
        print("\n🎨 Testing log level icons:")
        for level in LogLevel:
            msg = LogMessage(level, f"Test message for {level.value}")
            icon = msg._get_level_icon()
            print(f"  {level.value}: {icon}")
        
        print("✅ Log level icons work correctly")
    except Exception as e:
        print(f"❌ Error testing log level icons: {e}")
        return False
    
    # Test 4: Test operation tracking
    try:
        print("\n⏱️ Testing operation tracking:")
        
        # Simulate an operation
        operation_name = "Test Optimization"
        start_time = time.time()
        
        # Create messages for different stages
        start_msg = LogMessage(LogLevel.INFO, f"Starting {operation_name}...", operation_name)
        progress_msg = LogMessage(LogLevel.PROGRESS, "Processing...", operation_name, 50)
        end_msg = LogMessage(LogLevel.SUCCESS, f"Completed in {time.time() - start_time:.1f}s", operation_name)
        
        print(f"  Start: {start_msg.format_message()}")
        print(f"  Progress: {progress_msg.format_message()}")
        print(f"  End: {end_msg.format_message()}")
        
        print("✅ Operation tracking works correctly")
    except Exception as e:
        print(f"❌ Error testing operation tracking: {e}")
        return False
    
    print("\n🎉 All enhanced logging tests passed!")
    return True

def test_performance_integration():
    """Test integration with performance scripts"""
    print("\n🔧 Testing Performance Scripts Integration")
    print("=" * 50)
    
    try:
        from performance_scripts import PerformanceOptimizer
        optimizer = PerformanceOptimizer()
        
        print("✅ Successfully imported PerformanceOptimizer")
        
        # Test a simple operation
        print("\n🧪 Testing memory cache clear operation:")
        success, message = optimizer.clear_memory_cache()
        
        if success:
            print(f"✅ Operation successful: {message}")
        else:
            print(f"⚠️ Operation completed with issues: {message}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing performance integration: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 AbuSaker Tools - Enhanced Logging System Test")
    print("=" * 60)
    print(f"📅 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run tests
    logging_test = test_enhanced_logging()
    performance_test = test_performance_integration()
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"  Enhanced Logging: {'✅ PASS' if logging_test else '❌ FAIL'}")
    print(f"  Performance Integration: {'✅ PASS' if performance_test else '❌ FAIL'}")
    
    if logging_test and performance_test:
        print("\n🎉 All tests passed! Enhanced logging system is ready.")
        print("\n💡 You can now run the main application to see the enhanced logging in action:")
        print("   python main.py")
    else:
        print("\n⚠️ Some tests failed. Please check the error messages above.")
    
    print("\n👨‍💻 Enhanced logging system developed by Hamza Damra")

if __name__ == "__main__":
    main()
