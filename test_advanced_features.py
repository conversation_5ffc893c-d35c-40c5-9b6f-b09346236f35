"""
Test script for the new advanced optimization features
This script tests the new comprehensive optimization features without making system changes
"""
import sys
import os
import traceback

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all modules can be imported"""
    print("🔍 Testing imports...")
    
    try:
        from performance_scripts import PerformanceOptimizer, UNNECESSARY_SERVICES, ENHANCED_UNNECESSARY_PROCESSES
        print("✅ PerformanceOptimizer imported successfully")
        print(f"✅ Found {len(UNNECESSARY_SERVICES)} services to manage")
        print(f"✅ Found {len(ENHANCED_UNNECESSARY_PROCESSES)} processes to manage")
        
        from utils import run_command, is_admin
        print("✅ Utils imported successfully")
        
        from config import Config
        print("✅ Config imported successfully")
        
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        traceback.print_exc()
        return False

def test_optimizer_methods():
    """Test that all new optimizer methods exist"""
    print("\n🔧 Testing optimizer methods...")
    
    try:
        from performance_scripts import PerformanceOptimizer
        optimizer = PerformanceOptimizer()
        
        # Test new methods exist
        new_methods = [
            'disable_unnecessary_services',
            'optimize_visual_effects',
            'disable_xbox_features',
            'disable_windows_tips_and_ads',
            'enhanced_process_cleanup',
            'advanced_disk_cleanup',
            'optimize_startup_programs',
            'run_comprehensive_optimization'
        ]
        
        for method_name in new_methods:
            if hasattr(optimizer, method_name):
                print(f"✅ Method {method_name} exists")
            else:
                print(f"❌ Method {method_name} missing")
                return False
        
        return True
    except Exception as e:
        print(f"❌ Method test failed: {e}")
        traceback.print_exc()
        return False

def test_config_profiles():
    """Test that configuration profiles include advanced profile"""
    print("\n⚙️ Testing configuration profiles...")
    
    try:
        from config import Config
        config = Config()
        
        profiles = config.get("optimization_profiles", {})
        expected_profiles = ["gaming", "pubg_mobile", "basic", "advanced"]
        
        for profile in expected_profiles:
            if profile in profiles:
                print(f"✅ Profile '{profile}' exists")
                optimizations = profiles[profile].get("enabled_optimizations", [])
                print(f"   - Contains {len(optimizations)} optimizations")
            else:
                print(f"❌ Profile '{profile}' missing")
                return False
        
        # Check advanced profile has comprehensive optimizations
        advanced_profile = profiles.get("advanced", {})
        advanced_opts = advanced_profile.get("enabled_optimizations", [])
        if len(advanced_opts) >= 15:  # Should have many optimizations
            print(f"✅ Advanced profile has {len(advanced_opts)} optimizations")
        else:
            print(f"⚠️ Advanced profile only has {len(advanced_opts)} optimizations")
        
        return True
    except Exception as e:
        print(f"❌ Config test failed: {e}")
        traceback.print_exc()
        return False

def test_admin_check():
    """Test admin privilege checking"""
    print("\n🔐 Testing admin privileges...")
    
    try:
        from utils import is_admin
        admin_status = is_admin()
        
        if admin_status:
            print("✅ Running with Administrator privileges")
            print("   - All optimization features will be available")
        else:
            print("⚠️ Running without Administrator privileges")
            print("   - Some optimization features may be limited")
            print("   - For full functionality, run as Administrator")
        
        return True
    except Exception as e:
        print(f"❌ Admin check failed: {e}")
        traceback.print_exc()
        return False

def test_gui_compatibility():
    """Test GUI compatibility (import only)"""
    print("\n🖥️ Testing GUI compatibility...")
    
    try:
        import tkinter as tk
        print("✅ Tkinter available")
        
        # Test that main can be imported (but don't run it)
        import main
        print("✅ Main GUI module imported successfully")
        
        return True
    except Exception as e:
        print(f"❌ GUI test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("🎮 AbuSaker Tools - Advanced Features Test")
    print("=" * 60)
    print("🎯 Testing comprehensive Windows optimization features")
    print("👨‍💻 Developed by Hamza Damra")
    print("=" * 60)
    
    tests = [
        ("Import Test", test_imports),
        ("Optimizer Methods Test", test_optimizer_methods),
        ("Configuration Profiles Test", test_config_profiles),
        ("Admin Privileges Test", test_admin_check),
        ("GUI Compatibility Test", test_gui_compatibility)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Advanced features are ready to use.")
        print("\n🚀 You can now run the application with comprehensive optimization features:")
        print("   - Basic optimizations for quick performance boost")
        print("   - Advanced optimizations for comprehensive system tuning")
        print("   - Comprehensive optimization equivalent to the original script")
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
    
    print("\n💡 To run the full application:")
    print("   python main.py")
    print("\n🔧 For comprehensive optimization, use the 'advanced' profile")
    print("   or click the 'COMPREHENSIVE OPTIMIZATION' button")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
