"""
Utility functions for the Windows Performance Optimizer
"""
import ctypes
import sys
import os
import subprocess
import winreg
from typing import Optional, List


def is_admin() -> bool:
    """Check if the script is running with administrator privileges"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False


def run_as_admin():
    """Restart the script with administrator privileges"""
    if is_admin():
        return True
    else:
        # Re-run the program with admin rights
        ctypes.windll.shell32.ShellExecuteW(
            None, "runas", sys.executable, " ".join(sys.argv), None, 1
        )
        return False


def run_command(command: str, shell: bool = True, ignore_errors: bool = False) -> tuple:
    """
    Execute a system command and return the result

    Args:
        command: Command to execute
        shell: Whether to use shell
        ignore_errors: Whether to ignore command errors and return success based on execution

    Returns:
        tuple: (success: bool, output: str, error: str)
    """
    try:
        result = subprocess.run(
            command,
            shell=shell,
            capture_output=True,
            text=True,
            timeout=30
        )

        # If ignore_errors is True, consider the command successful if it executed
        # regardless of return code
        if ignore_errors:
            return True, result.stdout, result.stderr
        else:
            # Normal behavior - success based on return code
            success = result.returncode == 0
            return success, result.stdout, result.stderr

    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)


def get_registry_value(key_path: str, value_name: str, hkey=winreg.HKEY_LOCAL_MACHINE) -> Optional[str]:
    """
    Get a value from Windows registry
    
    Args:
        key_path: Registry key path
        value_name: Value name to retrieve
        hkey: Registry hive (default: HKEY_LOCAL_MACHINE)
        
    Returns:
        Registry value or None if not found
    """
    try:
        with winreg.OpenKey(hkey, key_path) as key:
            value, _ = winreg.QueryValueEx(key, value_name)
            return value
    except:
        return None


def set_registry_value(key_path: str, value_name: str, value, value_type=winreg.REG_DWORD, hkey=winreg.HKEY_LOCAL_MACHINE) -> bool:
    """
    Set a value in Windows registry
    
    Args:
        key_path: Registry key path
        value_name: Value name to set
        value: Value to set
        value_type: Registry value type
        hkey: Registry hive
        
    Returns:
        True if successful, False otherwise
    """
    try:
        with winreg.CreateKey(hkey, key_path) as key:
            winreg.SetValueEx(key, value_name, 0, value_type, value)
        return True
    except:
        return False


def format_bytes(bytes_value: int) -> str:
    """Convert bytes to human readable format"""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if bytes_value < 1024.0:
            return f"{bytes_value:.1f} {unit}"
        bytes_value /= 1024.0
    return f"{bytes_value:.1f} PB"


def get_emulator_processes() -> List[str]:
    """Get list of common emulator process names"""
    return [
        'HD-Player.exe',      # BlueStacks
        'Nox.exe',           # NoxPlayer
        'MEmu.exe',          # MEmu
        'LdPlayer.exe',      # LDPlayer
        'SmartGaGa.exe',     # SmartGaGa
        'GameLoop.exe',      # GameLoop
        'MSIAppPlayer.exe',  # MSI App Player
    ]


def log_action(action: str, success: bool, details: str = ""):
    """Log optimization actions"""
    status = "SUCCESS" if success else "FAILED"
    timestamp = __import__('datetime').datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_entry = f"[{timestamp}] {action}: {status}"
    if details:
        log_entry += f" - {details}"
    
    # Create logs directory if it doesn't exist
    os.makedirs("logs", exist_ok=True)
    
    # Write to log file
    with open("logs/optimization.log", "a", encoding="utf-8") as f:
        f.write(log_entry + "\n")
