"""
Main Window UI for AbuSaker Tools
PyQt5-based Professional PUBG-themed Interface
Adapted from external_ui.txt
Developed by Hamza Damra

Enhanced by SHADOWHACKER-GOD for professional UI/UX,
optimized spacing, and improved layout management.
"""

from PyQt5.QtCore import QSize, QRect, Qt
from PyQt5.QtGui import QFont, QFontDatabase, QIcon, QPixmap
from PyQt5.QtWidgets import (QMainWindow, QWidget, QLabel, QStackedWidget,
                             QPushButton, QFrame, QGridLayout, QHBoxLayout,
                             QComboBox, QLineEdit, QSizePolicy, QVBoxLayout,
                             QSpacerItem)

from .resources import resource_manager
from .styles import style_manager # Assuming this exists and can be expanded


class Ui_MainWindow(object):
    """Main Window UI Class - Professional PUBG-themed Interface"""

    def setupUi(self, MainWindow):
        """Setup the main UI components"""
        if not MainWindow.objectName():
            MainWindow.setObjectName("MainWindow")

        # Set window properties
        MainWindow.resize(1310, 739)
        MainWindow.setMinimumSize(QSize(1310, 739))
        MainWindow.setMaximumSize(QSize(1310, 739))

        # Load and set custom font
        self.font_family = resource_manager.load_font("AGENCYR.TTF")
        if self.font_family: # Ensure font is loaded
            font = QFont(self.font_family)
            font.setBold(True)
            font.setWeight(75)
            MainWindow.setFont(font)

        # Set window icon
        icon = QIcon()
        icon.addFile(resource_manager.get_icon_path("logo.ico"), QSize(), QIcon.Normal, QIcon.Off)
        MainWindow.setWindowIcon(icon)

        # Apply main window stylesheet
        MainWindow.setStyleSheet(self._get_main_stylesheet())

        # Setup central widget and main layout
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName("centralwidget")
        self.main_vertical_layout = QVBoxLayout(self.centralwidget)
        self.main_vertical_layout.setContentsMargins(0, 0, 0, 0)
        self.main_vertical_layout.setSpacing(0)

        # Setup background (using a QLabel to hold a potential image or solid color)
        self._setup_background()
        self.main_vertical_layout.addWidget(self.appbackground) # Add background to fill the central widget

        # Create a container widget for header and stacked widget to overlay on background
        self.overlay_widget = QWidget(self.centralwidget)
        self.overlay_widget.setObjectName("overlay_widget")
        # Use a grid layout for the overlay to manage header and content areas
        self.overlay_grid_layout = QGridLayout(self.overlay_widget)
        self.overlay_grid_layout.setContentsMargins(32, 20, 32, 20) # Enhanced overall margins for better breathing room
        self.overlay_grid_layout.setSpacing(24) # Increased spacing between header and content

        # Setup header elements
        self._setup_header()
        self.overlay_grid_layout.addLayout(self.header_horizontal_layout, 0, 0, 1, 3) # Row 0, spanning 3 columns for header

        # Setup navigation
        self._setup_navigation()
        self.overlay_grid_layout.addLayout(self.navigation_layout, 1, 0, 1, 3) # Row 1, spanning 3 columns for navigation

        # Setup stacked widget for pages
        self._setup_stacked_widget()
        self.overlay_grid_layout.addWidget(self.stackedWidget, 2, 0, 1, 3) # Row 2, spanning 3 columns

        # Setup pages
        self._setup_gfx_page()
        self._setup_other_page()
        self._setup_about_page()

        # Add the overlay widget to the main vertical layout, ensuring it covers the background
        self.main_vertical_layout.addWidget(self.overlay_widget)

        # Set central widget
        MainWindow.setCentralWidget(self.centralwidget)

        # Set initial text values
        self._set_initial_text()

        # Bring overlay widget to front for visibility over background
        self.overlay_widget.raise_()

    def _get_main_stylesheet(self):
        """Get the main window stylesheet, consolidated and professionalized."""
        return f"""
        QMainWindow {{
            background-color: #1A1A1A;
        }}

        QMenu::item {{
            background-color: #3A3A3A; /* Slightly lighter for menu items */
            color: #FFFFFF;
        }}
        QMenu::item:selected {{
            background-color: #FF6B35;
        }}

        QComboBox {{
            background-color: #2D2D2D;
            border: 2px solid #555555;
            border-radius: 6px; /* Slightly more rounded */
            text-align: center;
            color: #FFFFFF;
            padding: 7px 18px; /* Increased padding */
            font-family: "{self.font_family}";
            font-weight: bold;
            font-size: 13px; /* Slightly larger for readability */
            min-height: 35px; /* Ensure consistent height */
        }}
        QComboBox::drop-down {{
            border: none;
            width: 25px; /* Wider clickable area for dropdown arrow */
            subcontrol-origin: padding;
            subcontrol-position: right center;
        }}
        QComboBox::down-arrow {{
            image: url({resource_manager.get_icon_path("arrow_down.png")}); /* Custom arrow icon */
            width: 16px;
            height: 16px;
        }}
        QComboBox:hover {{
            border-color: #777777;
        }}

        QPushButton {{
            background-color: #2D2D2D;
            border: 2px solid #555555;
            border-radius: 6px; /* Slightly more rounded */
            color: #FFFFFF;
            padding: 10px 20px; /* Increased padding for larger buttons */
            font-family: "{self.font_family}";
            font-weight: bold;
            font-size: 15px; /* Slightly larger font */
            min-height: 30px; /* Base minimum height for all buttons */
        }}

        QPushButton:hover {{
            background-color: #3A3A3A; /* More subtle hover */
            border-color: #666666; /* Matching border color */
        }}

        QPushButton:checked,
        QPushButton:pressed {{
            background-color: #FF6B35;
            border-color: #E55A2B;
            color: #FFFFFF;
        }}

        QPushButton:disabled {{
            color: #606060; /* Darker disabled text */
            background-color: #202020; /* Darker disabled background */
            border-color: #353535; /* Darker disabled border */
        }}

        QLabel {{
            color: #FFFFFF;
            background-color: transparent;
            border: none;
        }}
        """

    def _setup_background(self):
        """Setup the main background. This will now be a QLabel that fills the central widget."""
        self.appbackground = QLabel(self.centralwidget)
        self.appbackground.setObjectName("appbackground")
        self.appbackground.setGeometry(self.centralwidget.rect()) # Initially fill central widget
        self.appbackground.setAlignment(Qt.AlignCenter) # Center image if loaded

        # Attempt to load a background image
        background_image_path = resource_manager.get_image_path("background.png") # Assuming a background image
        if background_image_path:
            pixmap = QPixmap(background_image_path)
            if not pixmap.isNull():
                self.appbackground.setPixmap(pixmap.scaled(self.appbackground.size(), Qt.KeepAspectRatioByExpanding, Qt.SmoothTransformation))
                self.appbackground.setStyleSheet("border: none;") # No border if image is used
            else:
                self.appbackground.setStyleSheet("background-color: #1A1A1A; border: none;")
        else:
            self.appbackground.setStyleSheet("background-color: #1A1A1A; border: none;")

        # Important: set size policy to expand with parent
        self.appbackground.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)


    def _setup_stacked_widget(self):
        """Setup the stacked widget for multiple pages"""
        self.stackedWidget = QStackedWidget(self.overlay_widget) # Parent to overlay_widget
        self.stackedWidget.setObjectName("stackedWidget")
        self.stackedWidget.setStyleSheet("""
            QStackedWidget {
                background-color: transparent;
                border: none;
            }
        """)
        # Ensure stacked widget can expand
        self.stackedWidget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)


    def _setup_gfx_page(self):
        """Setup the graphics settings page"""
        self.gfx_page = QWidget()
        self.gfx_page.setObjectName("gfx_page")

        self.gfx_page_layout = QVBoxLayout(self.gfx_page)
        self.gfx_page_layout.setContentsMargins(10, 10, 10, 10)
        self.gfx_page_layout.setSpacing(15)

        # Main content frame (now contains all graphics options)
        self.main_content_frame = QFrame(self.gfx_page)
        self.main_content_frame.setObjectName("main_content_frame")
        self.main_content_frame.setFrameShape(QFrame.NoFrame)
        self.main_content_frame.setStyleSheet("background-color: transparent;") # Make sure it's transparent

        self.main_content_grid_layout = QGridLayout(self.main_content_frame)
        self.main_content_grid_layout.setSpacing(32) # Enhanced spacing between major sections for better visual hierarchy
        self.main_content_grid_layout.setContentsMargins(24, 16, 24, 16) # Added content margins for better breathing room

        # Setup graphics frame
        self._setup_graphics_frame()
        self.main_content_grid_layout.addWidget(self.GraphicsFrame, 0, 0, 1, 2) # Row 0, spans 2 columns

        # Setup framerate frame
        self._setup_framerate_frame()
        self.main_content_grid_layout.addWidget(self.FramerateFrame, 1, 0, 1, 2) # Row 1, spans 2 columns

        # Setup style frame
        self._setup_style_frame()
        self.main_content_grid_layout.addWidget(self.StyleFrame, 2, 0, 1, 2) # Row 2, spans 2 columns

        # Setup shadow frame
        self._setup_shadow_frame()
        self.main_content_grid_layout.addWidget(self.ShadowFrame, 3, 0, 1, 1) # Row 3, column 0

        # Setup resolution frame
        self._setup_resolution_frame()
        self.main_content_grid_layout.addWidget(self.ResolutionkrFrame, 3, 1, 1, 1) # Row 3, column 1

        self.gfx_page_layout.addWidget(self.main_content_frame)

        # Action buttons layout (moved to a horizontal layout at the bottom)
        self.action_buttons_layout = QHBoxLayout()
        self.action_buttons_layout.setSpacing(20) # Enhanced spacing between buttons for better visual separation
        self.action_buttons_layout.setContentsMargins(24, 20, 24, 16) # Added margins for better button positioning

        self.action_buttons_layout.addItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)) # Larger spacer to push buttons right

        # PUBG choose frame (now integrated into action buttons area)
        self._setup_pubg_choose_frame()
        self.action_buttons_layout.addWidget(self.PubgchooseFrame)

        # Connect GameLoop button
        self._setup_connect_gameloop_button()
        self.action_buttons_layout.addWidget(self.connect_gameloop_btn)

        # Submit button
        self._setup_submit_button()
        self.action_buttons_layout.addWidget(self.submit_gfx_btn)

        self.gfx_page_layout.addLayout(self.action_buttons_layout)

        self.stackedWidget.addWidget(self.gfx_page)

    def _setup_submit_button(self):
        """Setup the submit button"""
        self.submit_gfx_btn = QPushButton()
        self.submit_gfx_btn.setObjectName("submit_gfx_btn")
        self.submit_gfx_btn.setMinimumSize(QSize(120, 50)) # Fixed size
        self.submit_gfx_btn.setMaximumSize(QSize(180, 60))

        font1 = QFont(self.font_family)
        font1.setPointSize(20)
        font1.setBold(True)
        self.submit_gfx_btn.setFont(font1)

        submit_style = f"""
        QPushButton {{
            background-color: #FF6B35;
            border: 2px solid #E55A2B;
            border-radius: 8px;
            color: #FFFFFF;
            font-family: "{self.font_family}";
            font-weight: bold;
            font-size: 16px;
            padding: 10px 20px;
        }}
        QPushButton:hover {{
            background-color: #E55A2B;
            border-color: #D4491F;
        }}
        QPushButton:pressed {{
            background-color: #D4491F;
            border-color: #C23E1A;
            color: #FFFFFF;
        }}
        QPushButton:disabled {{
            color: rgb(80, 80, 80);
            background-color: rgba(6, 6, 6, 200);
            border-color: #333333;
        }}
        """
        self.submit_gfx_btn.setStyleSheet(submit_style)

    def _setup_connect_gameloop_button(self):
        """Setup the connect GameLoop button"""
        self.connect_gameloop_btn = QPushButton()
        self.connect_gameloop_btn.setObjectName("connect_gameloop_btn")
        self.connect_gameloop_btn.setEnabled(True)
        self.connect_gameloop_btn.setMinimumSize(QSize(200, 50)) # Adjusted size
        self.connect_gameloop_btn.setMaximumSize(QSize(280, 60))

        font2 = QFont(self.font_family)
        font2.setPointSize(18) # Slightly smaller to fit text
        font2.setBold(True)
        self.connect_gameloop_btn.setFont(font2)

        gameloop_style = f"""
        QPushButton {{
            background-color: #2D2D2D;
            border: 2px solid #555555;
            border-radius: 8px;
            color: #FFFFFF;
            font-family: "{self.font_family}";
            font-weight: bold;
            font-size: 16px;
            padding: 10px 20px;
        }}
        QPushButton:hover {{
            background-color: #3D3D3D;
            border-color: #777777;
        }}
        QPushButton:checked {{
            background-color: #4CAF50; /* Green for connected status */
            border-color: #45A049;
            color: #FFFFFF;
        }}
        """
        self.connect_gameloop_btn.setStyleSheet(gameloop_style)
        self.connect_gameloop_btn.setCheckable(True)

    def _setup_pubg_choose_frame(self):
        """Setup the PUBG choose frame with a cleaner layout"""
        self.PubgchooseFrame = QFrame()
        self.PubgchooseFrame.setObjectName("PubgchooseFrame")
        self.PubgchooseFrame.setFrameShape(QFrame.NoFrame)
        self.PubgchooseFrame_layout = QVBoxLayout(self.PubgchooseFrame)
        self.PubgchooseFrame_layout.setContentsMargins(0, 0, 0, 0)
        self.PubgchooseFrame_layout.setSpacing(5) # Spacing between dropdown and label

        # PUBG choose label
        self.pubgchoose_label = QLabel(self.PubgchooseFrame)
        self.pubgchoose_label.setObjectName("pubgchoose_label")
        font5 = QFont(self.font_family)
        font5.setPointSize(10)
        font5.setBold(True)
        self.pubgchoose_label.setFont(font5)
        self.pubgchoose_label.setStyleSheet("""
        QLabel {
            color: #AAAAAA; /* Slightly subdued for descriptive text */
            background-color: transparent;
            border: none;
        }
        """)
        self.pubgchoose_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter) # Align left
        self.PubgchooseFrame_layout.addWidget(self.pubgchoose_label)

        # Horizontal layout for dropdown and button
        self.pubg_selection_layout = QHBoxLayout()
        self.pubg_selection_layout.setSpacing(8) # Spacing between dropdown and button

        # PUBG choose dropdown
        self.pubgchoose_dropdown = QComboBox(self.PubgchooseFrame)
        self.pubgchoose_dropdown.setObjectName("pubgchoose_dropdown")
        font4 = QFont(self.font_family)
        font4.setPointSize(13)
        font4.setBold(True)
        self.pubgchoose_dropdown.setFont(font4)
        self.pubgchoose_dropdown.setMinimumSize(QSize(160, 40)) # Consistent height
        self.pubgchoose_dropdown.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed) # Expand width

        self.pubg_selection_layout.addWidget(self.pubgchoose_dropdown)

        # PUBG choose button (e.g., to confirm selection or browse)
        self.pubgchoose_btn = QPushButton(self.PubgchooseFrame)
        self.pubgchoose_btn.setObjectName("pubgchoose_btn")
        self.pubgchoose_btn.setMinimumSize(QSize(60, 40)) # Smaller, action-oriented button
        self.pubgchoose_btn.setMaximumSize(QSize(80, 40))
        font3 = QFont(self.font_family)
        font3.setPointSize(16) # Smaller font for small button
        font3.setBold(True)
        self.pubgchoose_btn.setFont(font3)
        self.pubgchoose_btn.setText("SET") # More descriptive
        self.pubgchoose_btn.setFlat(False) # Make it look like a regular button
        self.pubgchoose_btn.setStyleSheet("""
            QPushButton {
                background-color: #3A3A3A;
                border: 1px solid #666666;
                border-radius: 5px;
                color: #FFFFFF;
                font-family: "AGENCYR"; /* Use custom font directly if it's universal */
                font-weight: bold;
                font-size: 16px;
                padding: 5px;
            }
            QPushButton:hover {
                background-color: #4A4A4A;
                border-color: #888888;
            }
            QPushButton:pressed {
                background-color: #FF6B35;
                border-color: #E55A2B;
            }
        """)

        self.pubg_selection_layout.addWidget(self.pubgchoose_btn)

        self.PubgchooseFrame_layout.addLayout(self.pubg_selection_layout)

    def _setup_other_page(self):
        """Setup the other tools page"""
        self.other_page = QWidget()
        self.other_page.setObjectName("other_page")
        # Add a simple label for now
        label = QLabel("Other Tools Page - Content Coming Soon!", self.other_page)
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet("color: #FFFFFF; font-size: 24px;")
        layout = QVBoxLayout(self.other_page)
        layout.addWidget(label)
        self.stackedWidget.addWidget(self.other_page)

    def _setup_about_page(self):
        """Setup the about page"""
        self.about_page = QWidget()
        self.about_page.setObjectName("about_page")
        # Add a simple label for now
        label = QLabel("About AbuSaker Tools\nDeveloped by Hamza Damra", self.about_page)
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet("color: #FFFFFF; font-size: 24px;")
        layout = QVBoxLayout(self.about_page)
        layout.addWidget(label)
        self.stackedWidget.addWidget(self.about_page)

    def _setup_header(self):
        """Setup header elements using a horizontal layout"""
        self.header_horizontal_layout = QHBoxLayout()
        self.header_horizontal_layout.setContentsMargins(16, 12, 16, 20) # Enhanced header margins for better positioning
        self.header_horizontal_layout.setSpacing(20) # Increased spacing between header elements

        # App name label
        self.appname_label = QLabel()
        self.appname_label.setObjectName("appname_label")
        font14 = QFont(self.font_family)
        font14.setPointSize(35)
        font14.setBold(True)
        self.appname_label.setFont(font14)
        self.appname_label.setStyleSheet("color: #FFFFFF; background-color: transparent;")
        self.header_horizontal_layout.addWidget(self.appname_label)

        self.header_horizontal_layout.addStretch(1) # Pushes controls to the right

        # Status labels (integrated into header or below it, depending on design)
        # For a professional look, status could be a small bar at the bottom.
        # For this re-do, I'll place them at the bottom left of the main window,
        # but the request was to fix header elements.
        # I'll create a placeholder for the status in the central widget's main layout
        # and keep header for app name and controls.
        self._setup_window_controls()
        self.header_horizontal_layout.addWidget(self.minimize_btn)
        self.header_horizontal_layout.addWidget(self.close_btn)


    def _setup_navigation(self):
        """Setup navigation buttons in a sidebar or top bar.
        For this refactor, I'll assume top bar navigation within the overlay_grid_layout.
        This will be placed in the header area, or a new row depending on the desired layout.
        For demonstration, I'll add them to a new horizontal layout below the main app name.
        """
        self.navigation_layout = QHBoxLayout()
        self.navigation_layout.setSpacing(20)  # Increased spacing between navigation buttons
        self.navigation_layout.setContentsMargins(24, 16, 24, 16)  # Added margins for better positioning

        nav_buttons = [
            ("gfx_nav_btn", "GFX Tools", self.gfx_page),
            ("other_nav_btn", "Other Tools", self.other_page),
            ("about_nav_btn", "About", self.about_page)
        ]

        # Use a group of buttons to ensure only one is checked at a time
        self.nav_button_group = []

        for btn_name, btn_text, target_page in nav_buttons:
            btn = QPushButton()
            btn.setObjectName(btn_name)
            btn.setText(btn_text)
            btn.setCheckable(True)
            btn.setFont(QFont(self.font_family, 14, QFont.Bold))
            btn.setMinimumSize(QSize(160, 50))  # Increased button size for better visibility
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #2D2D2D;
                    border: 2px solid #555555;
                    border-radius: 8px;
                    color: #FFFFFF;
                    padding: 12px 20px;
                    font-weight: bold;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: #3D3D3D;
                    border-color: #777777;
                }
                QPushButton:checked {
                    background-color: #FF6B35;
                    border-color: #E55A2B;
                    color: #FFFFFF;
                    font-weight: bold;
                }
            """)
            self.navigation_layout.addWidget(btn)
            self.nav_button_group.append(btn)
            btn.clicked.connect(lambda checked, page=target_page, btn_list=self.nav_button_group, current_btn=btn: self._on_nav_button_clicked(page, btn_list, current_btn))

        # Navigation layout is now added in the main setup method


    def _on_nav_button_clicked(self, target_page, all_buttons, clicked_button):
        """Handles navigation button clicks and updates checked state."""
        for btn in all_buttons:
            if btn is not clicked_button:
                btn.setChecked(False) # Uncheck others
        self.stackedWidget.setCurrentWidget(target_page)

    def _setup_graphics_frame(self):
        """Setup graphics quality selection frame"""
        self.GraphicsFrame = QFrame()
        self.GraphicsFrame.setObjectName("GraphicsFrame")
        self.GraphicsFrame.setFrameShape(QFrame.StyledPanel) # Add a subtle panel style
        self.GraphicsFrame.setStyleSheet("QFrame { background-color: #222222; border-radius: 8px; border: 1px solid #333333; }")

        self.graphics_frame_layout = QVBoxLayout(self.GraphicsFrame)
        self.graphics_frame_layout.setContentsMargins(15, 15, 15, 15)
        self.graphics_frame_layout.setSpacing(10)

        # Graphics label
        self.graphics_label = QLabel("Graphics")
        self.graphics_label.setObjectName("graphics_label")
        font6 = QFont(self.font_family)
        font6.setPointSize(23)
        font6.setBold(True)
        self.graphics_label.setFont(font6)
        self.graphics_label.setStyleSheet("color: #FFFFFF; background-color: transparent;")
        self.graphics_frame_layout.addWidget(self.graphics_label)

        # Graphics buttons layout
        self.GraphicsLayout = QHBoxLayout()
        self.GraphicsLayout.setSpacing(10) # Enhanced spacing between buttons
        self.GraphicsLayout.setContentsMargins(0, 0, 0, 0) # Managed by parent layout

        # Create graphics quality buttons
        sizePolicy = QSizePolicy(QSizePolicy.MinimumExpanding, QSizePolicy.Fixed)
        font3 = QFont(self.font_family)
        font3.setPointSize(16) # Adjusted font size for buttons
        font3.setBold(True)

        graphics_buttons_data = [
            ("smooth_graphics_btn", "Smooth"),
            ("balanced_graphics_btn", "Balanced"),
            ("hd_graphics_btn", "HD"),
            ("hdr_graphics_btn", "HDR"),
            ("ultrahd_graphics_btn", "Ultra HD"),
            ("uhd_graphics_btn", "UHD")
        ]

        self.graphics_button_group = [] # To manage exclusive checking

        for btn_name, btn_text in graphics_buttons_data:
            btn = QPushButton(self.GraphicsFrame) # Parent to frame
            btn.setObjectName(btn_name)
            btn.setSizePolicy(sizePolicy)
            btn.setMinimumSize(QSize(100, 45)) # Consistent button size
            btn.setFont(font3)
            btn.setCheckable(True)
            btn.setText(btn_text)

            if btn_name == "uhd_graphics_btn":
                btn.setEnabled(False)

            setattr(self, btn_name, btn)
            self.GraphicsLayout.addWidget(btn)
            self.graphics_button_group.append(btn)
            # Connect to a unified slot for exclusive checking
            btn.clicked.connect(lambda checked, btn_list=self.graphics_button_group, current_btn=btn: self._toggle_exclusive_button(btn_list, current_btn))

        self.graphics_frame_layout.addLayout(self.GraphicsLayout)

    def _setup_framerate_frame(self):
        """Setup framerate selection frame"""
        self.FramerateFrame = QFrame()
        self.FramerateFrame.setObjectName("FramerateFrame")
        self.FramerateFrame.setFrameShape(QFrame.StyledPanel)
        self.FramerateFrame.setStyleSheet("QFrame { background-color: #222222; border-radius: 8px; border: 1px solid #333333; }")

        self.framerate_frame_layout = QVBoxLayout(self.FramerateFrame)
        self.framerate_frame_layout.setContentsMargins(15, 15, 15, 15)
        self.framerate_frame_layout.setSpacing(10)

        # FPS label
        self.fps_label = QLabel("Frame Rate")
        self.fps_label.setObjectName("fps_label")
        font6 = QFont(self.font_family)
        font6.setPointSize(23)
        font6.setBold(True)
        self.fps_label.setFont(font6)
        self.fps_label.setStyleSheet("color: #FFFFFF; background-color: transparent;")
        self.framerate_frame_layout.addWidget(self.fps_label)

        # FPS buttons layout
        self.FramerateLayout = QHBoxLayout()
        self.FramerateLayout.setSpacing(10) # Enhanced spacing between buttons
        self.FramerateLayout.setContentsMargins(0, 0, 0, 0)

        # Create FPS buttons
        sizePolicy = QSizePolicy(QSizePolicy.MinimumExpanding, QSizePolicy.Fixed)
        font3 = QFont(self.font_family)
        font3.setPointSize(16)
        font3.setBold(True)

        fps_buttons_data = [
            ("low_fps_btn", "Low"),
            ("medium_fps_btn", "Medium"),
            ("high_fps_btn", "High"),
            ("ultra_fps_btn", "Ultra"),
            ("extreme_fps_btn", "Extreme"),
            ("fps90_fps_btn", "90 FPS"),
            ("fps120_fps_btn", "120 FPS")
        ]

        self.fps_button_group = [] # To manage exclusive checking

        for btn_name, btn_text in fps_buttons_data:
            btn = QPushButton(self.FramerateFrame)
            btn.setObjectName(btn_name)
            btn.setSizePolicy(sizePolicy)
            btn.setMinimumSize(QSize(100, 45)) # Consistent button size
            btn.setFont(font3)
            btn.setCheckable(True)
            btn.setText(btn_text)

            setattr(self, btn_name, btn)
            self.FramerateLayout.addWidget(btn)
            self.fps_button_group.append(btn)
            btn.clicked.connect(lambda checked, btn_list=self.fps_button_group, current_btn=btn: self._toggle_exclusive_button(btn_list, current_btn))

        self.framerate_frame_layout.addLayout(self.FramerateLayout)

    def _setup_style_frame(self):
        """Setup visual style selection frame"""
        self.StyleFrame = QFrame()
        self.StyleFrame.setObjectName("StyleFrame")
        self.StyleFrame.setEnabled(True)
        self.StyleFrame.setFrameShape(QFrame.StyledPanel)
        self.StyleFrame.setStyleSheet("QFrame { background-color: #222222; border-radius: 8px; border: 1px solid #333333; }")

        self.style_frame_layout = QVBoxLayout(self.StyleFrame)
        self.style_frame_layout.setContentsMargins(15, 15, 15, 15)
        self.style_frame_layout.setSpacing(10)

        # Style label
        self.style_label = QLabel("Style")
        self.style_label.setObjectName("style_label")
        font6 = QFont(self.font_family)
        font6.setPointSize(23)
        font6.setBold(True)
        self.style_label.setFont(font6)
        self.style_label.setStyleSheet("color: #FFFFFF; background-color: transparent;")
        self.style_frame_layout.addWidget(self.style_label)

        # Style buttons (now with icons or more descriptive elements)
        self.StyleButtonsLayout = QHBoxLayout()
        self.StyleButtonsLayout.setSpacing(15) # More spacing for visual buttons
        self.StyleButtonsLayout.setContentsMargins(0, 0, 0, 0)

        style_options_data = [
            ("classic_style_btn", "Classic", "style_classic.png"),
            ("colorful_style_btn", "Colorful", "style_colorful.png"),
            ("realistic_style_btn", "Realistic", "style_realistic.png"),
            ("soft_style_btn", "Soft", "style_soft.png"),
            ("movie_style_btn", "Movie", "style_movie.png")
        ]

        self.style_button_group = [] # To manage exclusive checking

        for btn_name, btn_text, icon_path in style_options_data:
            btn = QPushButton(self.StyleFrame)
            btn.setObjectName(btn_name)
            btn.setFixedSize(QSize(100, 100)) # Fixed size for icon buttons
            btn.setCheckable(True)

            # Use a QVBoxLayout for button content (icon + text)
            btn_inner_layout = QVBoxLayout(btn)
            btn_inner_layout.setContentsMargins(5, 5, 5, 5)
            btn_inner_layout.setSpacing(5)
            btn_inner_layout.setAlignment(Qt.AlignCenter)

            icon_label = QLabel()
            icon_pixmap = QPixmap(resource_manager.get_image_path(icon_path))
            if not icon_pixmap.isNull():
                icon_label.setPixmap(icon_pixmap.scaled(QSize(64, 64), Qt.KeepAspectRatio, Qt.SmoothTransformation))
            icon_label.setAlignment(Qt.AlignCenter)
            btn_inner_layout.addWidget(icon_label)

            text_label = QLabel(btn_text)
            text_label.setFont(QFont(self.font_family, 12, QFont.Bold))
            text_label.setStyleSheet("color: #FFFFFF; background-color: transparent;")
            text_label.setAlignment(Qt.AlignCenter)
            btn_inner_layout.addWidget(text_label)

            # Apply a general style to these icon buttons
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #333333;
                    border: 2px solid #555555;
                    border-radius: 8px;
                }
                QPushButton:hover {
                    background-color: #444444;
                    border-color: #777777;
                }
                QPushButton:checked {
                    background-color: #FF6B35;
                    border-color: #E55A2B;
                }
                QPushButton QLabel { /* Style for labels inside buttons */
                    color: #FFFFFF;
                }
                QPushButton:checked QLabel {
                    color: #FFFFFF;
                }
            """)
            setattr(self, btn_name, btn)
            self.StyleButtonsLayout.addWidget(btn)
            self.style_button_group.append(btn)
            btn.clicked.connect(lambda checked, btn_list=self.style_button_group, current_btn=btn: self._toggle_exclusive_button(btn_list, current_btn))


        self.style_frame_layout.addLayout(self.StyleButtonsLayout)

    def _setup_shadow_frame(self):
        """Setup shadow settings frame"""
        self.ShadowFrame = QFrame()
        self.ShadowFrame.setObjectName("ShadowFrame")
        self.ShadowFrame.setFrameShape(QFrame.StyledPanel)
        self.ShadowFrame.setStyleSheet("QFrame { background-color: #222222; border-radius: 8px; border: 1px solid #333333; }")

        self.shadow_frame_layout = QVBoxLayout(self.ShadowFrame)
        self.shadow_frame_layout.setContentsMargins(15, 15, 15, 15)
        self.shadow_frame_layout.setSpacing(10)

        # Shadow label
        self.shadow_label = QLabel("Shadow")
        self.shadow_label.setObjectName("shadow_label")
        font6 = QFont(self.font_family)
        font6.setPointSize(23)
        font6.setBold(True)
        self.shadow_label.setFont(font6)
        self.shadow_label.setStyleSheet("color: #FFFFFF; background-color: transparent;")
        self.shadow_frame_layout.addWidget(self.shadow_label)

        # Shadow options (e.g., enable/disable buttons)
        self.ShadowOptionsLayout = QHBoxLayout()
        self.ShadowOptionsLayout.setSpacing(10)

        self.shadow_on_btn = QPushButton("Enable")
        self.shadow_on_btn.setCheckable(True)
        self.shadow_on_btn.setMinimumSize(QSize(100, 45))
        self.shadow_on_btn.setFont(QFont(self.font_family, 16, QFont.Bold))

        self.shadow_off_btn = QPushButton("Disable")
        self.shadow_off_btn.setCheckable(True)
        self.shadow_off_btn.setMinimumSize(QSize(100, 45))
        self.shadow_off_btn.setFont(QFont(self.font_family, 16, QFont.Bold))

        self.shadow_button_group = [self.shadow_on_btn, self.shadow_off_btn]
        self.shadow_on_btn.clicked.connect(lambda checked: self._toggle_exclusive_button(self.shadow_button_group, self.shadow_on_btn))
        self.shadow_off_btn.clicked.connect(lambda checked: self._toggle_exclusive_button(self.shadow_button_group, self.shadow_off_btn))


        self.ShadowOptionsLayout.addWidget(self.shadow_on_btn)
        self.ShadowOptionsLayout.addWidget(self.shadow_off_btn)
        self.ShadowOptionsLayout.addStretch(1) # Push buttons to left

        self.shadow_frame_layout.addLayout(self.ShadowOptionsLayout)


    def _setup_resolution_frame(self):
        """Setup resolution settings frame"""
        self.ResolutionkrFrame = QFrame()
        self.ResolutionkrFrame.setObjectName("ResolutionkrFrame")
        self.ResolutionkrFrame.setFrameShape(QFrame.StyledPanel)
        self.ResolutionkrFrame.setStyleSheet("QFrame { background-color: #222222; border-radius: 8px; border: 1px solid #333333; }")

        self.resolution_frame_layout = QVBoxLayout(self.ResolutionkrFrame)
        self.resolution_frame_layout.setContentsMargins(15, 15, 15, 15)
        self.resolution_frame_layout.setSpacing(10)

        # Resolution label
        self.resolution_label = QLabel("Resolution (KR/JP)")
        self.resolution_label.setObjectName("resolution_label")
        font7 = QFont(self.font_family)
        font7.setPointSize(22)
        font7.setBold(True)
        self.resolution_label.setFont(font7)
        self.resolution_label.setStyleSheet("color: #FFFFFF; background-color: transparent;")
        self.resolution_frame_layout.addWidget(self.resolution_label)

        # Resolution dropdown
        self.resolution_dropdown = QComboBox()
        self.resolution_dropdown.setObjectName("resolution_dropdown")
        font_res = QFont(self.font_family, 14, QFont.Bold)
        self.resolution_dropdown.setFont(font_res)
        self.resolution_dropdown.setMinimumSize(QSize(180, 45)) # Consistent size
        self.resolution_dropdown.addItems(["Default", "720p", "1080p", "2K", "4K"]) # Example resolutions

        self.resolution_frame_layout.addWidget(self.resolution_dropdown)
        self.resolution_frame_layout.addStretch(1) # Push dropdown to top


    def _setup_status_labels(self):
        """Setup status labels at the bottom of the main window, outside the header layout."""
        self.status_horizontal_layout = QHBoxLayout()
        self.status_horizontal_layout.setContentsMargins(25, 0, 25, 15) # Margin from bottom and sides
        self.status_horizontal_layout.setSpacing(8)

        # App status label
        self.appstatus_label = QLabel()
        self.appstatus_label.setObjectName("appstatus_label")
        font15 = QFont(self.font_family)
        font15.setPointSize(18)
        font15.setBold(True)
        self.appstatus_label.setFont(font15)
        self.appstatus_label.setStyleSheet("color: #FFFFFF; background-color: transparent;")
        self.status_horizontal_layout.addWidget(self.appstatus_label)

        # App status text label
        self.appstatus_text_lable = QLabel()
        self.appstatus_text_lable.setObjectName("appstatus_text_lable")
        font16 = QFont(self.font_family)
        font16.setPointSize(15)
        font16.setBold(True)
        self.appstatus_text_lable.setFont(font16)
        self.appstatus_text_lable.setStyleSheet("color: #FFFFFF; background-color: transparent;")
        self.status_horizontal_layout.addWidget(self.appstatus_text_lable)

        self.status_horizontal_layout.addStretch(1) # Pushes labels to the left

        # Add this layout to the main vertical layout of the central widget, at the bottom
        self.main_vertical_layout.addLayout(self.status_horizontal_layout)


    def _setup_window_controls(self):
        """Setup window control buttons"""
        # Close button
        self.close_btn = QPushButton()
        self.close_btn.setObjectName("close_btn")
        self.close_btn.setFixedSize(QSize(40, 35)) # Smaller, consistent size

        font17 = QFont()
        font17.setFamily("MS Shell Dlg 2")
        font17.setPointSize(20) # Adjusted size
        font17.setBold(True)
        self.close_btn.setFont(font17)
        self.close_btn.setText("×")
        self.close_btn.setFlat(True)

        close_style = """
        QPushButton {
            background-color: none;
            border: none;
            color: #BBBBBB; /* Lighter color for controls */
            padding-top: -3px; /* Fine-tune text position */
        }
        QPushButton:hover {
            color: #FF6B35; /* Highlight on hover */
            background-color: #3A3A3A; /* Slight background on hover */
            border-radius: 5px;
        }
        QPushButton:pressed {
            color: #FFFFFF;
            background-color: #E55A2B; /* Active color */
        }
        """
        self.close_btn.setStyleSheet(close_style)

        # Minimize button
        self.minimize_btn = QPushButton()
        self.minimize_btn.setObjectName("minimize_btn")
        self.minimize_btn.setFixedSize(QSize(40, 35))

        font18 = QFont()
        font18.setFamily("MS Shell Dlg 2")
        font18.setPointSize(24) # Adjusted size
        font18.setBold(True)
        self.minimize_btn.setFont(font18)
        self.minimize_btn.setText("−")
        self.minimize_btn.setFlat(True)
        self.minimize_btn.setStyleSheet(close_style)

    def _set_initial_text(self):
        """Set initial text values for UI elements"""
        self.appname_label.setText("ABUSAKER TOOLS") # All caps for impact
        self.pubgchoose_label.setText("Choose PUBG Mobile Version:")
        self.submit_gfx_btn.setText("SUBMIT")
        self.connect_gameloop_btn.setText("CONNECT TO GAMELOOP")
        self.appstatus_label.setText("Status:")
        self.appstatus_text_lable.setText("Ready")

    def _toggle_exclusive_button(self, button_list, current_button):
        """Ensures only one button in a given list is checked."""
        for btn in button_list:
            if btn is not current_button:
                btn.setChecked(False)